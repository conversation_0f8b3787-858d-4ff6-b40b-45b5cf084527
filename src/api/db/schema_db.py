"""The database schema for quizforge."""

from datetime import datetime

from sqlmodel import Field, Relationship, SQLModel

from utils.enums import QuestionType


class User(SQLModel, table=True):
    """The user model."""

    id: int | None = Field(default=None, primary_key=True)
    email: str
    password: str
    created_at: datetime
    credits: int = Field(default=0)  # a user has to earn credits to create a quiz


class Question(SQLModel, table=True):
    """The question model."""

    id: int | None = Field(default=None, primary_key=True)
    answer: str
    question_type: str | QuestionType
    choice_options: dict[str, str]
    correct_answer: str

    quiz_id: int | None = Field(default=None, )


class Quiz(SQLModel, table=True):
    """The quiz model."""

    id: int | None = Field(default=None, primary_key=True)
    user_id: int
    document_id: int
    question_count: int
    created_at: datetime
